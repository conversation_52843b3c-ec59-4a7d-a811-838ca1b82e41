import { Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { StatusCodes } from 'http-status-codes';
import User from '../models/User';
import { sendOtpEmail } from './helper/OTP';
import { validateSignupData, sanitizeInput } from '../utils/validation';
import {
  ISignupRequest,
  ILoginRequest,
  IForgotPasswordRequest,
  IAuthResponse
} from '../interfaces/user.interface';


const JWT_SECRET = process.env.JWT_SECRET;
if (!JWT_SECRET) {
  throw new Error('JWT_SECRET is not defined in the environment variables');
}

// Signup Controller
export const signup = async (req: Request, res: Response): Promise<Response> => {
  try {
    const signupData: ISignupRequest = req.body;

    // Validate input data
    const validation = validateSignupData(signupData);
    if (!validation.isValid) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: validation.errors.join(', ')
      } as IAuthResponse);
    }

    // Sanitize input data
    const { fullname, username, email, phoneNumber, password } = {
      fullname: sanitizeInput(signupData.fullname),
      username: sanitizeInput(signupData.username.toLowerCase()),
      email: sanitizeInput(signupData.email.toLowerCase()),
      phoneNumber: sanitizeInput(signupData.phoneNumber),
      password: signupData.password,
    };

    // console.log('password in signup function', password);
    // const hashedPassword = await bcrypt.hash(password, 10);

    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [{ email }, { username }]
    });

    if (existingUser) {
      return res.status(StatusCodes.CONFLICT).json({
        success: false,
        message: existingUser.email === email ? 'Email already exists' : 'Username already exists'
      } as IAuthResponse);
    }

    // Create new user
    const newUser = new User({
      fullname,
      username: username.toLowerCase(),
      email: email.toLowerCase(),
      phoneNumber,
      password,
      role: 'Parent', // Default role
      isVerified: false
    });

    await newUser.save();

    // Generate OTP for email verification
    const otp = Math.floor(1000 + Math.random() * 9000).toString();
    
    // Send OTP email
    await sendOtpEmail(email, otp);

    // Store OTP in user document
    newUser.otp = otp;
    newUser.otpCreatedAt = new Date();
    await newUser.save();

    return res.status(StatusCodes.CREATED).json({
      success: true,
      message: 'User created successfully. Please verify your email with the OTP sent to your email address.',
      user: {
        id: newUser._id,
        fullname: newUser.fullname,
        username: newUser.username,
        email: newUser.email,
        isVerified: newUser.isVerified
      }
    } as IAuthResponse);

  } catch (error: any) {
    console.error('Signup error:', error);
    
    // Handle mongoose validation errors
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map((err: any) => err.message);
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: messages.join(', ')
      } as IAuthResponse);
    }

    // Handle duplicate key errors
    if (error.code === 11000) {
      const field = Object.keys(error.keyPattern)[0];
      return res.status(StatusCodes.CONFLICT).json({
        success: false,
        message: `${field} already exists`
      } as IAuthResponse);
    }

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Internal server error during signup'
    } as IAuthResponse);
  }
};


// Login Controller
export const login = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Username and password are required'
      } as IAuthResponse);
    }

   // Find user by username
    const user = await User.findOne({ username: username.toLowerCase() });

    if (!user) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'Invalid credentials or user not found'
      } as IAuthResponse);
    }

    console.log('password in login function', password);
    console.log('username in login function', username);
    console.log('user.password in login function', user.password);

    // Check password
    const isMatch = await bcrypt.compare(password, user.password);
    console.log('isPasswordValid in login function', isMatch);

    if (!isMatch) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'Password is incorrect'
      } as IAuthResponse);
    }

    // Check if user is verified
    if (!user.isVerified) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'Please verify your email before logging in'
      } as IAuthResponse);
    }

    // Generate JWT token
    const payload = { userId: user._id, email: user.email }
    const accessToken = jwt.sign(payload, JWT_SECRET,
      { expiresIn: '30d' });

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Login successful',
      user: {
        id: user._id,
        fullname: user.fullname,
        username: user.username,
        email: user.email,
        phoneNumber: user.phoneNumber,
        role: user.role,
        isVerified: user.isVerified
      },
      accessToken
    } as IAuthResponse);

  } catch (error) {
    console.error('Login error:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Internal server error during login'
    } as IAuthResponse);
  }
};

// Forgot Password Controller
export const forgotPassword = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { email, password, confirmPassword }: IForgotPasswordRequest = req.body;

    if (!email || !password || !confirmPassword) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Email, password, and confirm password are required'
      } as IAuthResponse);
    }

    const user = await User.findOne({ email });
    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'User not found'
      } as IAuthResponse);
    }

    if(!user.isVerified)
    {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'Please verify your email before resetting password'
      } as IAuthResponse);
    }

    const isSamePassword = await bcrypt.compare(password, user.password);
    if (isSamePassword) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'New password cannot be the same as the old password'
      } as IAuthResponse);
    }

    if (password !== confirmPassword) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Passwords do not match'
      } as IAuthResponse);
    }

    if (password.length < 6) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Password must be at least 6 characters long'
      } as IAuthResponse);
    }
    user.password = password;
    await user.save();

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Password reset successfully'
    } as IAuthResponse);

  } catch (error) {
    console.error('Forgot password error:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Internal server error during password reset'
    } as IAuthResponse);
  }
};
