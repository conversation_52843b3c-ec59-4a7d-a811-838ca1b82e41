"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyOtp = exports.resetPasswordGenerateOTP = exports.sendOtpEmail = void 0;
const nodemailer_1 = __importDefault(require("nodemailer"));
const dotenv_1 = __importDefault(require("dotenv"));
const http_status_codes_1 = require("http-status-codes");
const User_1 = __importDefault(require("../../models/User"));
dotenv_1.default.config();
const sendOtpEmail = (email_1, otp_1, ...args_1) => __awaiter(void 0, [email_1, otp_1, ...args_1], void 0, function* (email, otp, purpose = 'reset') {
    const transporter = nodemailer_1.default.createTransport({
        service: 'gmail',
        auth: {
            user: process.env.EMAIL_USER,
            pass: process.env.EMAIL_PASS
        }
    });
    const subject = purpose === 'signup' ? 'Email Verification OTP' : 'Password Reset OTP';
    const text = purpose === 'signup'
        ? `Welcome! Your OTP for email verification is: ${otp}. This OTP will expire in 2 minutes.`
        : `Your OTP for password reset is: ${otp}. This OTP will expire in 2 minutes.`;
    const mailOptions = {
        from: process.env.EMAIL_USER,
        to: email,
        subject,
        text
    };
    yield transporter.sendMail(mailOptions);
});
exports.sendOtpEmail = sendOtpEmail;
// Generate OTP and store it in the user's document (when user click on reset password it will generate OTP and send it to user's email)
const resetPasswordGenerateOTP = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const { email } = req.body;
    try {
        const user = yield User_1.default.findOne({ email });
        if (!user) {
            return res.status(http_status_codes_1.StatusCodes.NOT_FOUND).json({ message: 'Email not found' });
        }
        // Generate 4-digit OTP
        const otp = Math.floor(1000 + Math.random() * 9000).toString();
        // Send OTP to user's email
        yield (0, exports.sendOtpEmail)(email, otp);
        // Set OTP value to null before storing it (ensures old value is cleared)
        user['otp'] = "";
        user['otpCreatedAt'] = new Date(); // Store the current time as OTP generation time
        user['otp'] = otp; // Store the new OTP
        yield user.save();
        return res.status(http_status_codes_1.StatusCodes.OK).json({ message: 'OTP sent to your email' });
    }
    catch (error) {
        console.error(error);
        return res.status(http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR).json({ message: 'Error sending OTP', error });
    }
});
exports.resetPasswordGenerateOTP = resetPasswordGenerateOTP;
// Verify OTP function
const verifyOtp = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const { email, otp } = req.body;
    try {
        const user = yield User_1.default.findOne({ email });
        if (!user) {
            return res.status(http_status_codes_1.StatusCodes.NOT_FOUND).json({ message: 'Email not found' });
        }
        if (!user['otp']) {
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({ message: 'OTP has not been generated. Click Resend to generate a new one.' });
        }
        // Check if OTP is expired ... 2 minutes expiration time
        console.log("OTP Created At:", user['otpCreatedAt']);
        const otpCreatedAt = user['otpCreatedAt'] instanceof Date ? user['otpCreatedAt'].getTime() : 0;
        const otpAge = new Date().getTime() - otpCreatedAt;
        console.log("OTP Age in milliseconds:", otpAge);
        const otpExpirationTime = 2 * 60 * 1000; // 2 minutes in milliseconds
        if (otpAge > otpExpirationTime) {
            user['otp'] = "";
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({ message: 'OTP has expired. Please request a new OTP' });
        }
        if (user['otp'] !== otp) {
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({ message: 'OTP does not match' });
        }
        user['otp'] = "";
        yield user.save();
        return res.status(http_status_codes_1.StatusCodes.OK).json({ message: 'OTP verified' });
    }
    catch (error) {
        console.error(error);
        return res.status(http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR).json({ message: 'Error verifying OTP', error });
    }
});
exports.verifyOtp = verifyOtp;
