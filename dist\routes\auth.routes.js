"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_controller_1 = require("../controllers/auth.controller");
const OTP_1 = require("../controllers/helper/OTP");
const router = (0, express_1.Router)();
// Authentication Routes
router.post('/signup', auth_controller_1.signup);
router.post('/verify-signup-otp', auth_controller_1.verifySignupOTP);
router.post('/login', auth_controller_1.login);
// Password Reset Routes
router.post('/forgot-password/generate-otp', OTP_1.resetPasswordGenerateOTP);
router.post('/forgot-password/verify-otp', OTP_1.verifyOtp);
router.post('/forgot-password/reset', auth_controller_1.forgotPassword);
exports.default = router;
