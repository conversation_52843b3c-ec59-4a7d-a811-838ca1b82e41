"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.rateLimiter = void 0;
const express_rate_limit_1 = require("express-rate-limit");
const rateLimiter = () => {
    return (0, express_rate_limit_1.rateLimit)({
        windowMs: 15 * 60 * 1000,
        max: 500,
        message: { message: "Too many requests, please try again later." },
    });
};
exports.rateLimiter = rateLimiter;
