// import { Router, Request, Response } from 'express';
// import { StatusCodes } from 'http-status-codes';
// import { authenticateToken } from '../middlewares/auth.middleware';

// const router = Router();

// // Get user profile (protected route)
// router.get('/profile', authenticateToken, (req: Request, res: Response) => {
//   res.status(StatusCodes.OK).json({
//     success: true,
//     message: 'Profile retrieved successfully',
//     user: req.user
//   });
// });

// // Update user profile (protected route)
// router.put('/profile', authenticateToken, async (req: Request, res: Response) => {
//   try {
//     const { fullname, phoneNumber, dob, gender } = req.body;
//     const user = req.user!;

//     // Update allowed fields
//     if (fullname) user.fullname = fullname;
//     if (phoneNumber) user.phoneNumber = phoneNumber;
//     if (dob) user.dob = new Date(dob);
//     if (gender) user.gender = gender;

//     await user.save();

//     res.status(StatusCodes.OK).json({
//       success: true,
//       message: 'Profile updated successfully',
//       user
//     });

//   } catch (error) {
//     console.error('Update profile error:', error);
//     res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
//       success: false,
//       message: 'Internal server error'
//     });
//   }
// });

// export default router;
